// 测试"第二个位置"滚动逻辑
// 验证目标tab成为可视区域第二个的逻辑

console.log("=== 第二个位置滚动逻辑测试 ===");
console.log("Tab列表: 1-库存统计, 2-饲养管理, 3-生长监测, 4-接种记录, 5-疾病防控, 6-日常记录");
console.log("可视区域: 3个tab");
console.log("新规则: 滚动后目标tab成为可视区域的第二个");
console.log("");

// 模拟新的滚动逻辑
function calculateTargetScrollTab(targetTab, currentTab, currentVisibleStart, totalTabs = 6, visibleTabCount = 3) {
    if (totalTabs <= visibleTabCount) {
        return null;
    }

    const currentVisibleEnd = currentVisibleStart + visibleTabCount - 1;
    
    // 判断tab位置关系
    const isInVisibleArea = targetTab >= currentVisibleStart && targetTab <= currentVisibleEnd;
    const isOutsideLeft = targetTab < currentVisibleStart;
    const isOutsideRight = targetTab > currentVisibleEnd;

    let scrollToTab = null;

    if (isInVisibleArea) {
        // 在可视区域内，检查是否需要滚动
        const isAdjacentToActive = Math.abs(targetTab - currentTab) === 1;
        
        if (isAdjacentToActive) {
            // 点击相邻tab，不滚动
            return null;
        } else {
            // 点击间隔tab，需要滚动
            if (targetTab > currentTab) {
                // 向右滑动：让目标tab成为可视区域第二个
                scrollToTab = targetTab - visibleTabCount + 2;
            } else {
                // 向左滑动：让目标tab成为可视区域第二个
                scrollToTab = targetTab - 1;
            }
        }
    } else if (isOutsideLeft) {
        // 点击左侧外部tab，向右滑动，让目标tab成为第二个
        scrollToTab = targetTab - 1;
    } else if (isOutsideRight) {
        // 点击右侧外部tab，向左滑动，让目标tab成为第二个
        scrollToTab = targetTab - visibleTabCount + 2;
    }

    // 确保滚动目标在有效范围内
    if (scrollToTab !== null) {
        scrollToTab = Math.max(1, Math.min(scrollToTab, totalTabs - visibleTabCount + 1));
    }

    return scrollToTab;
}

// 测试用例
const secondPositionTestCases = [
    // 从左往右的场景
    {
        scenario: "从左往右滚动",
        currentVisible: 1,
        currentTab: 1,
        clickTab: 2,
        expected: null,
        desc: "库存统计激活，点击饲养管理（相邻）→ 不滚动"
    },
    {
        scenario: "从左往右滚动",
        currentVisible: 1,
        currentTab: 1,
        clickTab: 4,
        expected: 3,
        desc: "库存统计激活，点击接种记录（间隔）→ 滑动到3-5，接种记录成为第二个"
    },
    {
        scenario: "从左往右滚动",
        currentVisible: 1,
        currentTab: 1,
        clickTab: 5,
        expected: 4,
        desc: "库存统计激活，点击疾病防控（间隔）→ 滑动到4-6，疾病防控成为第二个"
    },
    
    // 从右往左的场景
    {
        scenario: "从右往左滚动",
        currentVisible: 4,
        currentTab: 6,
        clickTab: 5,
        expected: null,
        desc: "日常记录激活，点击疾病防控（相邻）→ 不滚动"
    },
    {
        scenario: "从右往左滚动",
        currentVisible: 4,
        currentTab: 6,
        clickTab: 3,
        expected: 2,
        desc: "日常记录激活，点击生长监测（间隔）→ 滑动到2-4，生长监测成为第二个"
    },
    {
        scenario: "从右往左滚动",
        currentVisible: 4,
        currentTab: 6,
        clickTab: 2,
        expected: 1,
        desc: "日常记录激活，点击饲养管理（间隔）→ 滑动到1-3，饲养管理成为第二个"
    },
    
    // 外部点击场景
    {
        scenario: "外部点击",
        currentVisible: 3,
        currentTab: 4,
        clickTab: 1,
        expected: 1,
        desc: "当前3-5，点击Tab1（左侧外部）→ 滑动到1-3，Tab1成为第二个（实际是第一个，边界处理）"
    },
    {
        scenario: "外部点击",
        currentVisible: 2,
        currentTab: 3,
        clickTab: 6,
        expected: 4,
        desc: "当前2-4，点击Tab6（右侧外部）→ 滑动到4-6，Tab6成为第二个"
    }
];

// 运行测试
let passCount = 0;
let totalCount = secondPositionTestCases.length;

secondPositionTestCases.forEach((testCase, index) => {
    const result = calculateTargetScrollTab(
        testCase.clickTab,
        testCase.currentTab,
        testCase.currentVisible,
        6, // totalTabs
        3  // visibleTabCount
    );
    
    const passed = result === testCase.expected;
    const status = passed ? "✅ PASS" : "❌ FAIL";
    
    if (passed) passCount++;
    
    console.log(`测试 ${index + 1}: ${status} [${testCase.scenario}]`);
    console.log(`  ${testCase.desc}`);
    console.log(`  当前可视: ${testCase.currentVisible}-${testCase.currentVisible + 2}`);
    console.log(`  当前激活: Tab${testCase.currentTab}`);
    console.log(`  点击: Tab${testCase.clickTab}`);
    console.log(`  预期: ${testCase.expected === null ? '不滚动' : 'tab-' + testCase.expected}`);
    console.log(`  实际: ${result === null ? '不滚动' : 'tab-' + result}`);
    
    if (result !== null) {
        const newStart = result;
        const newEnd = newStart + 2;
        console.log(`  新可视: ${newStart}-${newEnd}`);
        
        // 验证目标tab是否在第二个位置
        const targetPosition = testCase.clickTab - newStart + 1;
        if (targetPosition === 2) {
            console.log(`  ✅ Tab${testCase.clickTab} 确实在第二个位置`);
        } else if (targetPosition >= 1 && targetPosition <= 3) {
            console.log(`  ⚠️  Tab${testCase.clickTab} 在第${targetPosition}个位置（边界处理）`);
        } else {
            console.log(`  ❌ Tab${testCase.clickTab} 不在可视区域内`);
        }
    }
    console.log("");
});

console.log(`=== 测试结果: ${passCount}/${totalCount} 通过 ===`);

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { calculateTargetScrollTab, secondPositionTestCases };
}
