// 测试从后往前的滚动逻辑
// 重点验证从右往左滑动的场景

/**
 * 测试场景：6个tab，可视3个
 * 重点测试从后往前的滚动逻辑
 */

// 模拟 calculateTargetScrollTab 方法
function calculateTargetScrollTab(targetTab, currentVisibleStart, totalTabs, visibleTabCount) {
    if (totalTabs <= visibleTabCount) {
        return null;
    }

    const currentVisibleEnd = currentVisibleStart + visibleTabCount - 1;
    
    // 判断是否需要滚动
    const isAtLeftEdge = targetTab === currentVisibleStart;
    const isAtRightEdge = targetTab === currentVisibleEnd;
    const isOutsideLeft = targetTab < currentVisibleStart;
    const isOutsideRight = targetTab > currentVisibleEnd;

    // 检查是否有滚动空间
    const canScrollLeft = currentVisibleStart > 1;
    const canScrollRight = currentVisibleEnd < totalTabs;

    let scrollToTab = null;

    if ((isAtLeftEdge && canScrollLeft) || isOutsideLeft) {
        // 点击左边缘或左侧tab，向右滑动
        // 让目标tab成为可视区域的第一个
        scrollToTab = targetTab;
    } else if ((isAtRightEdge && canScrollRight) || isOutsideRight) {
        // 点击右边缘或右侧tab，向左滑动
        // 让目标tab成为可视区域的第一个
        const maxVisibleStart = totalTabs - visibleTabCount + 1;

        if (targetTab > maxVisibleStart) {
            // 如果目标tab超出最大可视起始位置，滚动到最右边
            scrollToTab = maxVisibleStart;
        } else {
            // 让目标tab成为可视区域的第一个
            scrollToTab = targetTab;
        }
    }

    if (scrollToTab !== null) {
        scrollToTab = Math.max(1, Math.min(scrollToTab, totalTabs - visibleTabCount + 1));
    }

    return scrollToTab;
}

// 专门测试从后往前的场景
const backwardTestCases = [
    // 从最右边开始往左
    { 
        currentVisible: 4, 
        clickTab: 4, 
        expected: 4, 
        desc: "当前4-6，点击Tab4（左边缘）→ 滑动到4-6（Tab4成为第一个）",
        expectedVisible: "4-6"
    },
    { 
        currentVisible: 4, 
        clickTab: 3, 
        expected: 3, 
        desc: "当前4-6，点击Tab3（左侧外部）→ 滑动到3-5（Tab3成为第一个）",
        expectedVisible: "3-5"
    },
    
    // 从中间往左
    { 
        currentVisible: 3, 
        clickTab: 3, 
        expected: 3, 
        desc: "当前3-5，点击Tab3（左边缘）→ 滑动到3-5（Tab3成为第一个）",
        expectedVisible: "3-5"
    },
    { 
        currentVisible: 3, 
        clickTab: 2, 
        expected: 2, 
        desc: "当前3-5，点击Tab2（左侧外部）→ 滑动到2-4（Tab2成为第一个）",
        expectedVisible: "2-4"
    },
    
    // 继续往左
    { 
        currentVisible: 2, 
        clickTab: 2, 
        expected: 2, 
        desc: "当前2-4，点击Tab2（左边缘）→ 滑动到2-4（Tab2成为第一个）",
        expectedVisible: "2-4"
    },
    { 
        currentVisible: 2, 
        clickTab: 1, 
        expected: 1, 
        desc: "当前2-4，点击Tab1（左侧外部）→ 滑动到1-3（Tab1成为第一个）",
        expectedVisible: "1-3"
    },
    
    // 到达最左边
    { 
        currentVisible: 1, 
        clickTab: 1, 
        expected: null, 
        desc: "当前1-3，点击Tab1（左边缘，已是最左）→ 不滚动",
        expectedVisible: "1-3"
    }
];

// 运行从后往前的测试
console.log("=== 从后往前滚动逻辑测试 ===");
console.log("总tab数: 6, 可视tab数: 3");
console.log("");

let passCount = 0;
let totalCount = backwardTestCases.length;

backwardTestCases.forEach((testCase, index) => {
    const result = calculateTargetScrollTab(
        testCase.clickTab, 
        testCase.currentVisible, 
        6, // totalTabs
        3  // visibleTabCount
    );
    
    const passed = result === testCase.expected;
    const status = passed ? "✅ PASS" : "❌ FAIL";
    
    if (passed) passCount++;
    
    console.log(`测试 ${index + 1}: ${status}`);
    console.log(`  ${testCase.desc}`);
    console.log(`  预期滚动: ${testCase.expected === null ? '不滚动' : 'tab-' + testCase.expected}`);
    console.log(`  实际滚动: ${result === null ? '不滚动' : 'tab-' + result}`);
    console.log(`  预期可视: ${testCase.expectedVisible}`);
    console.log("");
});

console.log(`=== 测试结果: ${passCount}/${totalCount} 通过 ===`);

// 如果有失败的测试，显示详细信息
if (passCount < totalCount) {
    console.log("\n=== 失败测试分析 ===");
    backwardTestCases.forEach((testCase, index) => {
        const result = calculateTargetScrollTab(
            testCase.clickTab, 
            testCase.currentVisible, 
            6, 3
        );
        
        if (result !== testCase.expected) {
            console.log(`测试 ${index + 1} 失败:`);
            console.log(`  场景: ${testCase.desc}`);
            console.log(`  预期: ${testCase.expected}`);
            console.log(`  实际: ${result}`);
            console.log(`  分析: 需要检查边缘触发条件和滚动计算逻辑`);
            console.log("");
        }
    });
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { calculateTargetScrollTab, backwardTestCases };
}
