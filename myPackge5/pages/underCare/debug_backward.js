// 调试从后往前的滚动逻辑
// 模拟实际的6个tab场景

console.log("=== 调试从后往前的滚动逻辑 ===");
console.log("Tab列表: 1-库存统计, 2-饲养管理, 3-生长监测, 4-接种记录, 5-疾病防控, 6-日常记录");
console.log("可视区域: 3个tab");
console.log("");

// 模拟滚动逻辑
function debugScroll(currentStart, clickTab, totalTabs = 6, visibleCount = 3) {
    const currentEnd = currentStart + visibleCount - 1;
    
    console.log(`--- 场景分析 ---`);
    console.log(`当前可视区域: ${currentStart}-${currentEnd}`);
    console.log(`点击Tab: ${clickTab}`);
    
    // 判断条件
    const isAtLeftEdge = clickTab === currentStart;
    const isAtRightEdge = clickTab === currentEnd;
    const isOutsideLeft = clickTab < currentStart;
    const isOutsideRight = clickTab > currentEnd;
    const canScrollLeft = currentStart > 1;
    const canScrollRight = currentEnd < totalTabs;
    
    console.log(`判断条件:`);
    console.log(`  isAtLeftEdge: ${isAtLeftEdge}`);
    console.log(`  isAtRightEdge: ${isAtRightEdge}`);
    console.log(`  isOutsideLeft: ${isOutsideLeft}`);
    console.log(`  isOutsideRight: ${isOutsideRight}`);
    console.log(`  canScrollLeft: ${canScrollLeft}`);
    console.log(`  canScrollRight: ${canScrollRight}`);
    
    let scrollToTab = null;
    let action = "不滚动";
    
    if (isOutsideLeft) {
        scrollToTab = clickTab;
        action = `向右滑动到tab-${scrollToTab}，让Tab${clickTab}成为第一个`;
    } else if (isAtLeftEdge && canScrollLeft) {
        scrollToTab = clickTab;
        action = `向右滑动到tab-${scrollToTab}，让Tab${clickTab}成为第一个`;
    } else if (isOutsideRight) {
        const maxVisibleStart = totalTabs - visibleCount + 1;
        if (clickTab > maxVisibleStart) {
            scrollToTab = maxVisibleStart;
            action = `向左滑动到tab-${scrollToTab}（最右边）`;
        } else {
            scrollToTab = clickTab;
            action = `向左滑动到tab-${scrollToTab}，让Tab${clickTab}成为第一个`;
        }
    } else if (isAtRightEdge && canScrollRight) {
        const maxVisibleStart = totalTabs - visibleCount + 1;
        if (clickTab > maxVisibleStart) {
            scrollToTab = maxVisibleStart;
            action = `向左滑动到tab-${scrollToTab}（最右边）`;
        } else {
            scrollToTab = clickTab;
            action = `向左滑动到tab-${scrollToTab}，让Tab${clickTab}成为第一个`;
        }
    }
    
    console.log(`结果: ${action}`);
    
    if (scrollToTab !== null) {
        const newStart = scrollToTab;
        const newEnd = newStart + visibleCount - 1;
        console.log(`新可视区域: ${newStart}-${newEnd}`);
    }
    
    console.log("");
    return scrollToTab;
}

// 测试从后往前的具体场景
console.log("=== 从后往前滚动测试 ===");

// 场景1: 从最右边开始
console.log("🔍 场景1: 当前可视4-6，点击Tab4（接种记录，左边缘）");
debugScroll(4, 4);

console.log("🔍 场景2: 当前可视4-6，点击Tab3（生长监测，左侧外部）");
debugScroll(4, 3);

// 场景2: 继续往左
console.log("🔍 场景3: 当前可视3-5，点击Tab3（生长监测，左边缘）");
debugScroll(3, 3);

console.log("🔍 场景4: 当前可视3-5，点击Tab2（饲养管理，左侧外部）");
debugScroll(3, 2);

// 场景3: 继续往左
console.log("🔍 场景5: 当前可视2-4，点击Tab2（饲养管理，左边缘）");
debugScroll(2, 2);

console.log("🔍 场景6: 当前可视2-4，点击Tab1（库存统计，左侧外部）");
debugScroll(2, 1);

// 场景4: 到达最左边
console.log("🔍 场景7: 当前可视1-3，点击Tab1（库存统计，左边缘，已是最左）");
debugScroll(1, 1);

console.log("=== 测试完成 ===");
console.log("请检查每个场景的滚动逻辑是否符合预期：");
console.log("- 点击边缘tab时，该tab应该成为第一个");
console.log("- 从右往左滑动时，逻辑应该与从左往右一致");
