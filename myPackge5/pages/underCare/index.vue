<!--
智能Tab滚动系统使用说明（边缘触发版本）：

1. 扩展性配置：
   - totalTabs: 设置总tab数量，支持任意数量
   - visibleTabCount: 设置可视区域完整显示的tab数量，默认3个
   - 可通过 setTotalTabs(count) 和 setVisibleTabCount(count) 动态调整

2. 相邻触发滚动逻辑：
   - 可视区域显示 visibleTabCount 个完整tab + 1个半隐藏tab
   - 点击相邻tab：不滚动
   - 点击间隔tab（向右）：滑动，让该tab成为最后一个
   - 点击间隔tab（向左）：滑动，让该tab成为第一个
   - 点击可视区域外的tab：滑动到合适位置
   - 自动处理边界情况（最左、最右）

3. 相邻触发示例（6个tab，可视3个）：
   - 库存统计激活，点击饲养管理（相邻）→ 不滚动
   - 库存统计激活，点击生长监测（间隔）→ 滑动，生长监测成为最后一个
   - 日常记录激活，点击疾病防控（相邻）→ 不滚动
   - 日常记录激活，点击接种记录（间隔）→ 滑动，接种记录成为第一个

4. 添加新tab步骤：
   - 在template中添加新的tab-item，设置正确的id="tab-N"
   - 在switchTab方法的typeMap中添加对应映射
   - 调用 setTotalTabs(newCount) 更新总数量
   - 添加对应的内容组件

示例：添加第7个tab
- 模板：<view class="tab-item" @click="switchTab(7)" :id="'tab-7'">新Tab</view>
- 映射：typeMap中添加 7: 'newType'
- 更新：this.setTotalTabs(7)
-->
<template>
    <view>
        <CustomNavbar :bgColor="'#08BA7E'" :titleColor="'#FFFFFF'" />
        <view class="header">
            <view class="fifter">
                <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/shaixuan.png"
                    alt="" @click="fifterClick" />
            </view>
        </view>

        <view class="tab-container">
            <scroll-view class="tabs-scroll" scroll-x="true" show-scrollbar="false" scroll-with-animation="true"
                :scroll-into-view="scrollIntoView">
                <view class="tabs">
                    <view class="tab-item" :class="{ 'active': currentTab === 1 }" @click="switchTab(1)" :id="'tab-1'">
                        <view class="icon-box" :class="{ 'active': currentTab === 1 }">
                            <image :src="`${obs}/nmb-mini/mine/kucunguanli.png`" class="tab-icon" />
                        </view>
                        库存统计
                        <view v-if="currentTab === 1" class="bubble-arrow"></view>
                    </view>
                    <view class="tab-item" :class="{ 'active': currentTab === 2 }" @click="switchTab(2)" :id="'tab-2'">
                        <view class="icon-box" :class="{ 'active': currentTab === 2 }">
                            <image :src="`${obs}/nmb-mini/mine/siyang.png`" class="tab-icon" />
                        </view>
                        饲养管理
                        <view v-if="currentTab === 2" class="bubble-arrow"></view>
                    </view>
                    <view class="tab-item" :class="{ 'active': currentTab === 3 }" @click="switchTab(3)" :id="'tab-3'">
                        <view class="icon-box" :class="{ 'active': currentTab === 3 }">
                            <image :src="`${obs}/nmb-mini/mine/shengzhang.png`" class="tab-icon" />
                        </view>
                        生长监测
                        <view v-if="currentTab === 3" class="bubble-arrow"></view>
                    </view>
                    <view class="tab-item" :class="{ 'active': currentTab === 4 }" @click="switchTab(4)" :id="'tab-4'">
                        <view class="icon-box" :class="{ 'active': currentTab === 4 }">
                            <image :src="`${obs}/nmb-mini/mine/jeizhong.png`" class="tab-icon" />
                        </view>
                        接种记录
                        <view v-if="currentTab === 4" class="bubble-arrow"></view>
                    </view>
                    <view class="tab-item" :class="{ 'active': currentTab === 5 }" @click="switchTab(5)" :id="'tab-5'">
                        <view class="icon-box" :class="{ 'active': currentTab === 5 }">
                            <image :src="`${obs}/nmb-mini/mine/jibing.png`" class="tab-icon" />
                        </view>
                        疾病防控
                        <view v-if="currentTab === 5" class="bubble-arrow"></view>
                    </view>
                    <view class="tab-item" :class="{ 'active': currentTab === 6 }" @click="switchTab(6)" :id="'tab-6'">
                        <view class="icon-box" :class="{ 'active': currentTab === 6 }">
                            <image :src="`${obs}/nmb-mini/mine/richang.png`" class="tab-icon" />
                        </view>
                        日常记录
                        <view v-if="currentTab === 6" class="bubble-arrow"></view>
                    </view>
                </view>
            </scroll-view>
        </view>

        <view class="tab-content">
            <InventoryStatistics v-if="currentTab === 1" :filterParams="filterParams" :resetSearch="resetSearchFlag" />
            <FeedingManagement v-if="currentTab === 2" :filterParams="filterParams" :resetSearch="resetSearchFlag" />
            <GrowthMonitoring v-if="currentTab === 3" :filterParams="filterParams" :resetSearch="resetSearchFlag" />
            <VaccinationRecord v-if="currentTab === 4" :filterParams="filterParams" :resetSearch="resetSearchFlag" />
            <DiseaseControl v-if="currentTab === 5" :filterParams="filterParams" :resetSearch="resetSearchFlag" />
            <DailyRecord v-if="currentTab === 6" :filterParams="filterParams" :resetSearch="resetSearchFlag" />
        </view>
        <filterPopup @resetSearch="resetSearch" :filterType="filterType" :pickerFilterShow="pickerFilterShow"
            @canel="pickerFilterShow = false" @submitForm="submitForm" />
    </view>
</template>

<script>
import filterPopup from './components/filterPopup.vue'
import CustomNavbar from '@/components/uni-custom-navbar/CustomNavbar.vue'
import InventoryStatistics from './components/InventoryStatistics.vue'
import FeedingManagement from './components/FeedingManagement.vue'
import GrowthMonitoring from './components/GrowthMonitoring.vue'
import VaccinationRecord from './components/VaccinationRecord.vue'
import DiseaseControl from './components/DiseaseControl.vue'
import DailyRecord from './components/DailyRecord.vue'
const app = getApp();

export default {
    components: {
        CustomNavbar,
        filterPopup,
        InventoryStatistics,
        FeedingManagement,
        GrowthMonitoring,
        VaccinationRecord,
        DiseaseControl,
        DailyRecord
    },
    data() {
        return {
            obs: app.globalData.obs,
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            pickerFilterShow: false,
            filters: {},
            list: [],
            currentTab: 1, // 当前选中的tab
            scrollIntoView: '', // 滚动到指定元素
            totalTabs: 6, // 总tab数量
            visibleTabCount: 3, // 可视区域完全显示的tab数量
            currentVisibleStart: 1, // 当前可视区域起始位置
            filterType: 'inventory',
            filterParams: {}, // 筛选参数
            resetSearchFlag: false // 重置搜索标志
        }
    },
    onLoad() {
        // 示例：如果需要动态调整tab数量，可以在这里设置
        // this.setTotalTabs(8); // 设置为8个tab
        // this.setVisibleTabCount(4); // 设置可视区域显示4个tab
    },
    onUnload() {
    },
    onShow() { },
    computed: {

    },
    methods: {
        switchTab(tabIndex) {
            const typeMap = { 1: 'inventory', 2: 'breeding', 3: 'growth', 4: 'vaccination', 5: 'disease', 6: 'daily' };

            // 切换tab时清空筛选条件
            this.filterParams = {};
            this.resetSearchFlag = !this.resetSearchFlag;
            this.currentTab = tabIndex;
            this.filterType = typeMap[tabIndex];

            this.$nextTick(() => {
                const targetScrollTab = this.calculateTargetScrollTab(tabIndex);
                if (targetScrollTab !== null) {
                    // 更新当前可视区域起始位置
                    this.updateCurrentVisibleStart(targetScrollTab);
                    // 执行滚动
                    this.scrollIntoView = `tab-${targetScrollTab}`;
                    setTimeout(() => {
                        this.scrollIntoView = '';
                    }, 300);
                }
            });
        },



        // 计算目标滚动tab - 边缘触发滚动逻辑
        // 支持任意数量tab的扩展，点击边缘tab时自动滑动
        calculateTargetScrollTab(targetTab) {
            // 如果总tab数不超过可视数量，不需要滚动
            if (this.totalTabs <= this.visibleTabCount) {
                console.log('总tab数不超过可视数量，无需滚动');
                return null;
            }

            // 获取当前可视区域
            const currentVisibleStart = this.getCurrentVisibleStart();
            const currentVisibleEnd = currentVisibleStart + this.visibleTabCount - 1;

            console.log(`当前可视区域: ${currentVisibleStart}-${currentVisibleEnd}, 目标tab: ${targetTab}`);

            // 相邻触发滚动逻辑：点击间隔tab时才滚动
            let scrollToTab = null;

            // 判断tab位置关系
            const isInVisibleArea = targetTab >= currentVisibleStart && targetTab <= currentVisibleEnd;
            const isOutsideLeft = targetTab < currentVisibleStart;
            const isOutsideRight = targetTab > currentVisibleEnd;

            if (isInVisibleArea) {
                // 在可视区域内，检查是否需要滚动
                const isAdjacentToActive = Math.abs(targetTab - this.currentTab) === 1;

                if (isAdjacentToActive) {
                    // 点击相邻tab，不滚动
                    console.log('点击相邻tab，不滚动');
                    return null;
                } else {
                    // 点击间隔tab，需要滚动
                    if (targetTab > this.currentTab) {
                        // 向右滑动：让目标tab成为可视区域最后一个
                        console.log('点击间隔tab，向右滑动，目标tab成为最后一个');
                        scrollToTab = targetTab - this.visibleTabCount + 1;
                    } else {
                        // 向左滑动：让目标tab成为可视区域第一个
                        console.log('点击间隔tab，向左滑动，目标tab成为第一个');
                        scrollToTab = targetTab;
                    }
                }
            } else if (isOutsideLeft) {
                // 点击左侧外部tab，向右滑动，让目标tab成为第一个
                console.log('点击左侧外部tab，向右滑动，目标tab成为第一个');
                scrollToTab = targetTab;
            } else if (isOutsideRight) {
                // 点击右侧外部tab，向左滑动，让目标tab成为最后一个
                console.log('点击右侧外部tab，向左滑动，目标tab成为最后一个');
                scrollToTab = targetTab - this.visibleTabCount + 1;
            }

            // 确保滚动目标在有效范围内
            if (scrollToTab !== null) {
                scrollToTab = Math.max(1, Math.min(scrollToTab, this.totalTabs - this.visibleTabCount + 1));
                console.log(`计算滚动目标: tab-${scrollToTab}`);
            } else {
                console.log('目标tab在可视区域中间，无需滚动');
            }

            return scrollToTab;
        },

        // 获取当前可视区域的起始位置 - 基于滚动状态追踪
        getCurrentVisibleStart() {
            // 使用实例变量追踪当前可视区域起始位置
            if (!this.currentVisibleStart) {
                this.currentVisibleStart = 1; // 初始状态从第一个tab开始
            }
            return this.currentVisibleStart;
        },

        // 更新当前可视区域起始位置
        updateCurrentVisibleStart(newStart) {
            this.currentVisibleStart = newStart;
            console.log('更新当前可视区域起始位置:', newStart);
        },

        // 动态设置tab数量 - 支持扩展
        setTotalTabs(count) {
            this.totalTabs = count;
            // 重置可视区域到开始位置
            this.currentVisibleStart = 1;
            console.log(`设置总tab数量为: ${count}`);
        },

        // 动态设置可视tab数量 - 支持不同屏幕尺寸
        setVisibleTabCount(count) {
            this.visibleTabCount = count;
            // 重新计算当前可视区域
            this.currentVisibleStart = Math.min(this.currentVisibleStart, this.totalTabs - count + 1);
            console.log(`设置可视tab数量为: ${count}`);
        },
        // 搜索
        fifterClick() {
            this.pickerFilterShow = true
        },

        resetSearch() {
            this.filterParams = {};
            this.resetSearchFlag = !this.resetSearchFlag;
        },
        submitForm(val) {
            this.filterParams = { ...val };
            this.pickerFilterShow = false;
        },
    },
}
</script>

<style lang="scss" scoped>
.header {
    width: 750rpx;
    height: 727rpx;
    display: flex;
    padding-top: 120rpx;
    /* 导航栏空间 */
    box-sizing: border-box;
    position: relative;
    background: url(https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/bg.png) no-repeat 100% 100%;
    background-size: 100% 100%;
    position: relative;
}

.fifter {
    position: absolute;
    top: 195rpx;
    right: 30rpx;

    img {
        width: 34rpx;
        height: 32.5rpx;
    }
}

.tab-container {
    margin-top: -372rpx;
    padding: 0 26rpx;
    // margin-bottom: 30rpx;
    overflow: visible;
}

.tabs-scroll {
    width: 100%;
    white-space: nowrap;
    overflow: visible;
    padding-bottom: 15rpx;
}

.tabs {
    display: flex;
    align-items: center;
    width: max-content;
    padding: 0 10rpx;
    overflow: visible;
    height: 100rpx;
}

.tab-item {
    width: 188rpx;
    height: 65rpx;
    background: #FFFFFF;
    border-radius: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1DB17A;
    font-weight: 500;
    font-size: 26rpx;
    position: relative;
    transition: all 0.3s ease;
    margin-right: 17rpx;
    flex-shrink: 0;
    padding: 5rpx 20rpx 5rpx 5rpx;

    .icon-box {
        width: 50rpx;
        height: 50rpx;
        background-color: #E3FFEE;
        border-radius: 50%;
        margin-right: 10rpx;
        padding: 12rpx;
        box-sizing: border-box;

        &.active {
            background-color: #ffffff !important;
        }

        .tab-icon {
            width: 25rpx;
            height: 25rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    &:last-child {
        margin-right: 10rpx;
    }

    &.active {
        color: #FFFFFF;
        background: linear-gradient(140deg, #1CC271 0%, #5CD26F 100%);
    }
}

.bubble-arrow {
    position: absolute;
    bottom: -10rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10rpx solid transparent;
    border-right: 10rpx solid transparent;
    border-top: 10rpx solid #5CD26F;
    z-index: 999;
}

.tab-content {
    padding: 0 30rpx;
    height: calc(100vh - 500rpx);
    overflow: auto;
}

.main {
    margin-top: -372rpx;
}

.Add {
    width: 152rpx;
    height: 145rpx;
    position: absolute;
    bottom: 290rpx;
    right: 10rpx;

    img {
        width: 152rpx;
        height: 145rpx;
    }
}
</style>
