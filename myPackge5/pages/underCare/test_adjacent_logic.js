// 测试相邻触发滚动逻辑
// 验证新的滚动规则：点击相邻tab不滚动，点击间隔tab才滚动

console.log("=== 相邻触发滚动逻辑测试 ===");
console.log("Tab列表: 1-库存统计, 2-饲养管理, 3-生长监测, 4-接种记录, 5-疾病防控, 6-日常记录");
console.log("可视区域: 3个tab");
console.log("");

// 模拟新的滚动逻辑
function calculateTargetScrollTab(targetTab, currentTab, currentVisibleStart, totalTabs = 6, visibleTabCount = 3) {
    if (totalTabs <= visibleTabCount) {
        return null;
    }

    const currentVisibleEnd = currentVisibleStart + visibleTabCount - 1;
    
    // 判断tab位置关系
    const isInVisibleArea = targetTab >= currentVisibleStart && targetTab <= currentVisibleEnd;
    const isOutsideLeft = targetTab < currentVisibleStart;
    const isOutsideRight = targetTab > currentVisibleEnd;

    let scrollToTab = null;

    if (isInVisibleArea) {
        // 在可视区域内，检查是否需要滚动
        const isAdjacentToActive = Math.abs(targetTab - currentTab) === 1;
        
        if (isAdjacentToActive) {
            // 点击相邻tab，不滚动
            return null;
        } else {
            // 点击间隔tab，需要滚动
            if (targetTab > currentTab) {
                // 向右滑动：让目标tab成为可视区域最后一个
                scrollToTab = targetTab - visibleTabCount + 1;
            } else {
                // 向左滑动：让目标tab成为可视区域第一个
                scrollToTab = targetTab;
            }
        }
    } else if (isOutsideLeft) {
        // 点击左侧外部tab，向右滑动，让目标tab成为第一个
        scrollToTab = targetTab;
    } else if (isOutsideRight) {
        // 点击右侧外部tab，向左滑动，让目标tab成为最后一个
        scrollToTab = targetTab - visibleTabCount + 1;
    }

    // 确保滚动目标在有效范围内
    if (scrollToTab !== null) {
        scrollToTab = Math.max(1, Math.min(scrollToTab, totalTabs - visibleTabCount + 1));
    }

    return scrollToTab;
}

// 测试用例
const adjacentTestCases = [
    // 从左往右的场景
    {
        scenario: "从左往右滚动",
        currentVisible: 1,
        currentTab: 1,
        clickTab: 2,
        expected: null,
        desc: "库存统计激活，点击饲养管理（相邻）→ 不滚动"
    },
    {
        scenario: "从左往右滚动",
        currentVisible: 1,
        currentTab: 1,
        clickTab: 4,
        expected: 2,
        desc: "库存统计激活，点击接种记录（间隔）→ 滑动到2-4，接种记录成为最后一个"
    },
    {
        scenario: "从左往右滚动",
        currentVisible: 1,
        currentTab: 2,
        clickTab: 1,
        expected: null,
        desc: "饲养管理激活，点击库存统计（相邻）→ 不滚动"
    },
    {
        scenario: "从左往右滚动",
        currentVisible: 1,
        currentTab: 2,
        clickTab: 3,
        expected: null,
        desc: "饲养管理激活，点击生长监测（相邻）→ 不滚动"
    },
    
    // 从右往左的场景
    {
        scenario: "从右往左滚动",
        currentVisible: 4,
        currentTab: 6,
        clickTab: 5,
        expected: null,
        desc: "日常记录激活，点击疾病防控（相邻）→ 不滚动"
    },
    {
        scenario: "从右往左滚动",
        currentVisible: 4,
        currentTab: 6,
        clickTab: 4,
        expected: 4,
        desc: "日常记录激活，点击接种记录（间隔）→ 滑动到4-6，接种记录成为第一个"
    },
    {
        scenario: "从右往左滚动",
        currentVisible: 4,
        currentTab: 5,
        clickTab: 6,
        expected: null,
        desc: "疾病防控激活，点击日常记录（相邻）→ 不滚动"
    },
    {
        scenario: "从右往左滚动",
        currentVisible: 4,
        currentTab: 5,
        clickTab: 4,
        expected: null,
        desc: "疾病防控激活，点击接种记录（相邻）→ 不滚动"
    },
    
    // 外部点击场景
    {
        scenario: "外部点击",
        currentVisible: 2,
        currentTab: 3,
        clickTab: 1,
        expected: 1,
        desc: "当前2-4，点击Tab1（左侧外部）→ 滑动到1-3"
    },
    {
        scenario: "外部点击",
        currentVisible: 2,
        currentTab: 3,
        clickTab: 5,
        expected: 3,
        desc: "当前2-4，点击Tab5（右侧外部）→ 滑动到3-5"
    }
];

// 运行测试
let passCount = 0;
let totalCount = adjacentTestCases.length;

adjacentTestCases.forEach((testCase, index) => {
    const result = calculateTargetScrollTab(
        testCase.clickTab,
        testCase.currentTab,
        testCase.currentVisible,
        6, // totalTabs
        3  // visibleTabCount
    );
    
    const passed = result === testCase.expected;
    const status = passed ? "✅ PASS" : "❌ FAIL";
    
    if (passed) passCount++;
    
    console.log(`测试 ${index + 1}: ${status} [${testCase.scenario}]`);
    console.log(`  ${testCase.desc}`);
    console.log(`  当前可视: ${testCase.currentVisible}-${testCase.currentVisible + 2}`);
    console.log(`  当前激活: Tab${testCase.currentTab}`);
    console.log(`  点击: Tab${testCase.clickTab}`);
    console.log(`  预期: ${testCase.expected === null ? '不滚动' : 'tab-' + testCase.expected}`);
    console.log(`  实际: ${result === null ? '不滚动' : 'tab-' + result}`);
    
    if (result !== null) {
        const newStart = result;
        const newEnd = newStart + 2;
        console.log(`  新可视: ${newStart}-${newEnd}`);
    }
    console.log("");
});

console.log(`=== 测试结果: ${passCount}/${totalCount} 通过 ===`);

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { calculateTargetScrollTab, adjacentTestCases };
}
