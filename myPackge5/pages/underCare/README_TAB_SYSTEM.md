# 智能Tab滚动系统使用指南

## 系统特点

1. **高扩展性**：支持任意数量的tab，无需修改滚动逻辑
2. **智能滚动**：严格按照需求表格逻辑，自动计算最优滚动位置
3. **自适应**：支持不同屏幕尺寸和可视区域配置
4. **易维护**：添加新tab只需简单配置，无需修改复杂逻辑

## 核心配置参数

```javascript
data() {
    return {
        totalTabs: 6,           // 总tab数量
        visibleTabCount: 3,     // 可视区域完整显示的tab数量
        currentVisibleStart: 1, // 当前可视区域起始位置（自动维护）
        currentTab: 1,          // 当前激活的tab
    }
}
```

## 滚动逻辑说明

### 基本规则（边缘触发滚动）
- 可视区域显示 `visibleTabCount` 个完整tab + 1个半隐藏tab
- 点击可视区域中间的tab：不滚动
- 点击可视区域左边缘的tab：向右滑动（如果左边还有tab）
- 点击可视区域右边缘的tab：向左滑动（如果右边还有tab）
- 点击可视区域外的tab：滑动到让该tab可见

### 边缘触发示例（6个tab，可视3个）

| 点击Tab | 当前可视 | 触发条件 | 滚动方向 | 目标可视 | 滚动到 |
|---------|----------|----------|----------|----------|--------|
| Tab 1   | 1-3      | 左边缘，但已是最左 | 无 | 1-3 | - |
| Tab 1   | 2-4      | 左侧外部 | 右滑 | 1-3 | tab-1 |
| Tab 2   | 2-4      | 左边缘，左边有tab | 右滑 | 1-3 | tab-1 |
| Tab 3   | 1-3      | 右边缘，右边有tab | 左滑 | 2-4 | tab-2 |
| Tab 4   | 2-4      | 右边缘，右边有tab | 左滑 | 3-5 | tab-3 |
| Tab 6   | 3-5      | 右边缘，但已是最右 | 左滑 | 4-6 | tab-4 |

### 滚动触发条件详解
1. **左边缘触发**：点击可视区域第一个tab，且左边还有tab
2. **右边缘触发**：点击可视区域最后一个tab，且右边还有tab
3. **外部触发**：点击不在可视区域内的tab
4. **中间不触发**：点击可视区域中间的tab（非边缘）

## 如何添加新Tab

### 步骤1：在模板中添加tab-item
```html
<view class="tab-item" :class="{ 'active': currentTab === 7 }" 
      @click="switchTab(7)" :id="'tab-7'">
    <view class="icon-box" :class="{ 'active': currentTab === 7 }">
        <image :src="`${obs}/nmb-mini/mine/new-icon.png`" class="tab-icon" />
    </view>
    新功能
    <view v-if="currentTab === 7" class="bubble-arrow"></view>
</view>
```

### 步骤2：添加内容组件
```html
<NewComponent v-if="currentTab === 7" :filterParams="filterParams" :resetSearch="resetSearchFlag" />
```

### 步骤3：更新配置
```javascript
// 在switchTab方法中添加映射
const typeMap = { 
    1: 'inventory', 2: 'breeding', 3: 'growth', 
    4: 'vaccination', 5: 'disease', 6: 'daily',
    7: 'newFeature'  // 新增
};

// 更新总tab数量
this.setTotalTabs(7);
```

## 动态配置方法

### 设置总tab数量
```javascript
this.setTotalTabs(8); // 设置为8个tab
```

### 设置可视tab数量
```javascript
this.setVisibleTabCount(4); // 可视区域显示4个tab
```

## 扩展示例

### 8个tab，可视4个的配置
```javascript
onLoad() {
    this.setTotalTabs(8);
    this.setVisibleTabCount(4);
}
```

### 10个tab，可视5个的配置
```javascript
onLoad() {
    this.setTotalTabs(10);
    this.setVisibleTabCount(5);
}
```

## 注意事项

1. **ID命名规范**：tab的id必须按照 `tab-N` 格式命名
2. **顺序编号**：tab编号必须从1开始连续递增
3. **类型映射**：switchTab方法中的typeMap需要包含所有tab的映射
4. **组件导入**：新增tab对应的组件需要正确导入和注册

## 调试信息

系统会在控制台输出详细的滚动计算过程：
- 当前可视区域范围
- 目标tab位置
- 计算的滚动目标
- 可视区域起始位置更新

这些信息有助于理解和调试滚动逻辑。
