# 智能Tab滚动系统使用指南

## 系统特点

1. **高扩展性**：支持任意数量的tab，无需修改滚动逻辑
2. **智能滚动**：严格按照需求表格逻辑，自动计算最优滚动位置
3. **自适应**：支持不同屏幕尺寸和可视区域配置
4. **易维护**：添加新tab只需简单配置，无需修改复杂逻辑

## 核心配置参数

```javascript
data() {
    return {
        totalTabs: 6,           // 总tab数量
        visibleTabCount: 3,     // 可视区域完整显示的tab数量
        currentVisibleStart: 1, // 当前可视区域起始位置（自动维护）
        currentTab: 1,          // 当前激活的tab
    }
}
```

## 滚动逻辑说明

### 基本规则（相邻触发滚动 - 目标tab成为第二个）
- 可视区域显示 `visibleTabCount` 个完整tab + 1个半隐藏tab
- **点击相邻tab**：不滚动（相邻指与当前激活tab相差1个位置）
- **点击间隔tab（向右）**：滑动，让该tab成为可视区域第二个
- **点击间隔tab（向左）**：滑动，让该tab成为可视区域第二个
- **点击可视区域外的tab**：滑动到合适位置，让该tab成为第二个
- **核心逻辑**：滚动后目标tab始终在第二个位置（居中显示）

### 相邻触发示例（6个tab，可视3个 - 目标tab成为第二个）

| 当前激活 | 当前可视 | 点击Tab | 关系 | 滚动行为 | 目标可视 | 目标位置 | 说明 |
|----------|----------|---------|------|----------|----------|----------|------|
| Tab1（库存统计） | 1-3 | Tab2（饲养管理） | 相邻 | 不滚动 | 1-3 | - | 相邻tab不滚动 |
| Tab1（库存统计） | 1-3 | Tab4（接种记录） | 间隔 | 左滑 | 3-5 | 第二个 | Tab4成为第二个 |
| Tab6（日常记录） | 4-6 | Tab5（疾病防控） | 相邻 | 不滚动 | 4-6 | - | 相邻tab不滚动 |
| Tab6（日常记录） | 4-6 | Tab3（生长监测） | 间隔 | 右滑 | 2-4 | 第二个 | Tab3成为第二个 |
| Tab3（生长监测） | 2-4 | Tab1（库存统计） | 外部 | 右滑 | 1-3 | 第一个* | 边界处理 |
| Tab3（生长监测） | 2-4 | Tab6（日常记录） | 外部 | 左滑 | 4-6 | 第二个 | Tab6成为第二个 |

*注：当目标tab是Tab1时，由于边界限制，只能成为第一个

### 滚动触发条件详解
1. **相邻不触发**：点击与当前激活tab相邻的tab（相差1个位置）
2. **间隔触发**：点击与当前激活tab间隔的tab（相差2个或更多位置）
3. **外部触发**：点击不在可视区域内的tab
4. **核心判断**：基于当前激活tab的位置，而不是可视区域边缘

## 如何添加新Tab

### 步骤1：在模板中添加tab-item
```html
<view class="tab-item" :class="{ 'active': currentTab === 7 }" 
      @click="switchTab(7)" :id="'tab-7'">
    <view class="icon-box" :class="{ 'active': currentTab === 7 }">
        <image :src="`${obs}/nmb-mini/mine/new-icon.png`" class="tab-icon" />
    </view>
    新功能
    <view v-if="currentTab === 7" class="bubble-arrow"></view>
</view>
```

### 步骤2：添加内容组件
```html
<NewComponent v-if="currentTab === 7" :filterParams="filterParams" :resetSearch="resetSearchFlag" />
```

### 步骤3：更新配置
```javascript
// 在switchTab方法中添加映射
const typeMap = { 
    1: 'inventory', 2: 'breeding', 3: 'growth', 
    4: 'vaccination', 5: 'disease', 6: 'daily',
    7: 'newFeature'  // 新增
};

// 更新总tab数量
this.setTotalTabs(7);
```

## 动态配置方法

### 设置总tab数量
```javascript
this.setTotalTabs(8); // 设置为8个tab
```

### 设置可视tab数量
```javascript
this.setVisibleTabCount(4); // 可视区域显示4个tab
```

## 扩展示例

### 8个tab，可视4个的配置
```javascript
onLoad() {
    this.setTotalTabs(8);
    this.setVisibleTabCount(4);
}
```

### 10个tab，可视5个的配置
```javascript
onLoad() {
    this.setTotalTabs(10);
    this.setVisibleTabCount(5);
}
```

## 注意事项

1. **ID命名规范**：tab的id必须按照 `tab-N` 格式命名
2. **顺序编号**：tab编号必须从1开始连续递增
3. **类型映射**：switchTab方法中的typeMap需要包含所有tab的映射
4. **组件导入**：新增tab对应的组件需要正确导入和注册

## 调试信息

系统会在控制台输出详细的滚动计算过程：
- 当前可视区域范围
- 目标tab位置
- 计算的滚动目标
- 可视区域起始位置更新

这些信息有助于理解和调试滚动逻辑。

库存统计、饲养管理、生长监测、接种记录、疾病防控、日常记录

